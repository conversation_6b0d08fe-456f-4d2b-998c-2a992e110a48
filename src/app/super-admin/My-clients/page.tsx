"use client";

import React, { useEffect, useRef, useState } from "react";
import { Lu<PERSON><PERSON>cil } from "react-icons/lu";
import { FaChevronDown, FaCheck } from "react-icons/fa6";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { FiFilter, FiDownload, FiTrash2 } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { HiPlus } from "react-icons/hi";
import Link from "next/link";
import { ClientType, SuperAdminType } from "@/utils/types";
import { useRouter } from "next/navigation";
import { backendApiClient } from "@/utils/apiClient";
import { useMutation, useQuery } from "@tanstack/react-query";
import { formatDate } from "@/utils/commonFunction";
import { toast } from "react-toastify";
import { queryClient } from "@/hooks/useGlobalContext";

/* ----------------------------- Types ----------------------------- */

type SuperAdminsResponse = {
  success: boolean;
  data: SuperAdminType[];
  meta: unknown;
};

type ClientsResponse = {
  success: boolean;
  data: ClientType[];
  meta: unknown;
};

/* ----------------------- Accordion Section ------------------------ */

function AccordionSection({
  title,
  items,
  onPress,
  selectedPermissions,
}: {
  title: string;
  items: { id: string; description: string }[];
  onPress: (id: string) => void;
  selectedPermissions: string[];
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4">
      <div
        className="flex cursor-pointer items-center justify-between pb-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <label className="flex cursor-pointer items-center gap-3">
          <span className="text-[14px] font-medium text-[#050013] peer-checked:text-[#050013]">
            {title}
          </span>
        </label>
        <span className="bg-tables rounded-full p-1">
          <FaChevronDown
            className={`h-3 w-3 text-gray-500 transition-transform duration-200 ${
              isOpen ? "rotate-180" : "rotate-0"
            }`}
          />
        </span>
      </div>
      {isOpen && (
        <div className="mt-2 mb-6">
          {items.map((item, idx) => {
            console.log(selectedPermissions, "------", item.id);

            return (
              <label
                key={idx}
                className="mb-2 flex cursor-pointer items-center space-x-2"
              >
                <div className="relative flex items-center">
                  <input
                    type="checkbox"
                    className="peer hidden"
                    checked={selectedPermissions.includes(item.id)}
                    onChange={() => onPress(item.id)}
                  />
                  <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]">
                    {" "}
                  </span>
                  <FaCheck
                    size={12}
                    className={`absolute left-[3px] transition-opacity duration-200 peer-checked:text-[#18EC94] peer-checked:opacity-100 peer-[&:not(:checked)]:opacity-0`}
                  />
                </div>
                <span className="text-sm text-[#76787A] peer-checked:text-[#050013]">
                  {item.description}
                </span>
              </label>
            );
          })}
        </div>
      )}
    </div>
  );
}

// --- Super Admins Table Component ---
interface SuperAdminsTableProps {
  data: SuperAdminType[];
  onView: (user: SuperAdminType) => void;
  onEdit: (user: SuperAdminType) => void;
  onDelete: (id: string) => void;
}

function SuperAdminsTable({
  data,
  onView,
  onEdit,
  onDelete,
}: SuperAdminsTableProps) {
  return (
    <table className="min-w-full table-auto text-left text-[11px]">
      <thead className="text-[#76787A]">
        <tr>
          <th className="px-4 py-3 font-medium">Date</th>
          <th className="px-4 py-3 font-medium">User Name</th>
          <th className="px-4 py-3 font-medium">Role Name</th>
          <th className="px-4 py-3 font-medium">Status</th>
          <th className="px-4 py-3 text-center">Action</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-100">
        {data.map((row) => (
          <tr key={row.id} className="hover:bg-[#E4FFF4]">
            <td className="px-4 py-3 text-[#050013]">
              {formatDate(row.createdAt)}
            </td>
            <td
              className="cursor-pointer px-4 py-3 font-medium text-[#050013]"
              onClick={() => onView(row)}
            >
              {row.firstName} {row.lastName}
            </td>
            <td className="px-4 py-3 text-[#050013]">{row.role}</td>
            <td className="px-4 py-3">
              <span
                className={`flex items-center gap-1 ${row.isActive ? "text-[#13BB76]" : "text-[#8C8B9F]"}`}
              >
                <span className="h-[6px] w-[6px] rounded-full bg-current" />
                {row.isActive ? "Active" : "Inactive"}
              </span>
            </td>
            <td className="px-4 py-3">
              <div className="flex justify-center gap-3">
                <LuPencil
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                  onClick={() => onEdit(row)}
                />
                <FiTrash2
                  onClick={() => onDelete(row.id)}
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

// --- My Clients Table Component ---
interface ClientsTableProps {
  data: ClientType[];
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: string) => void;
}

function ClientsTable({ data, onDelete, onStatusChange }: ClientsTableProps) {
  const { push } = useRouter();

  return (
    <table className="min-w-full table-auto text-left text-[11px]">
      <thead className="text-[#76787A]">
        <tr>
          <th className="px-4 py-3 font-medium text-nowrap">Client Name</th>
          <th className="px-4 py-3 font-medium text-nowrap">Date Added</th>
          <th className="px-4 py-3 font-medium text-nowrap">Primary Contact</th>
          <th className="px-4 py-3 font-medium text-nowrap">Status</th>
          <th className="px-4 py-3 text-center">Action</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-100">
        {data.map((row) => (
          <tr key={row.id} className="hover:bg-[#E4FFF4]">
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              <Link href={`My-clients/${row.id}`} className="text-[#050013]">
                {row.companyName}
              </Link>
            </td>
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              {formatDate(row.createdAt)}
            </td>
            <td className="px-4 py-3 text-nowrap text-[#050013]">
              {row.primaryContactName}
            </td>
            <td
              className="cursor-pointer px-4 py-3"
              onClick={() =>
                onStatusChange(
                  row.id,
                  row.status === "active" ? "inactive" : "active",
                )
              }
            >
              <span
                className={`flex items-center gap-1 ${row.status === "active" ? "text-[#13BB76]" : "text-[#8C8B9F]"}`}
              >
                <span className="h-[6px] w-[6px] rounded-full bg-current" />
                <span className="capitalize">{row.status}</span>
              </span>
            </td>
            <td className="px-4 py-3">
              <div className="flex justify-center gap-3">
                <LuPencil
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                  onClick={() => push(`My-clients/edit/${row.id}`)}
                />
                <FiTrash2
                  className="h-4 w-4 cursor-pointer text-[#050013]"
                  onClick={() => onDelete(row.id)}
                />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

/* ---------------------- Permission Sections ---------------------- */

const accessSections = [
  {
    title: "User & Role Management",
    items: [
      {
        id: "e517e8ec-795a-4471-b729-18b179b545f4",
        description: "Create, edit, delete, Dispatchers, Drivers, Users",
      },
      {
        id: "b28e7232-c1c5-49a7-8ab0-48218a594faf",
        description: "Assign Admins to taxi companies",
      },
      {
        id: "871c5291-f907-496f-b55a-29fb15f5e269",
        description: "Manage permissions for all roles",
      },
      {
        id: "de07c45c-188c-4c55-9c48-d07e5daab8ba",
        description: "Suspend/reactivate any user",
      },
    ],
  },
  {
    title: "Booking & Dispatch Control",
    items: [
      {
        id: "325e82e4-9936-4160-982a-dac40b3ee5dd",
        description: "View, edit, and manage all bookings",
      },
      {
        id: "8cbb59d3-eb1a-484e-b85a-3ff18dd46ff6",
        description: "Assign/reassign drivers to rides",
      },
      {
        id: "f4a8109a-78fc-4410-84ad-c9917592ae41",
        description: "View driver live locations",
      },
      {
        id: "1f86c3d5-998b-4c55-893c-031d52a3142a",
        description: "Cancel or reschedule any ride",
      },
    ],
  },
  {
    title: "Company & Financial Management",
    items: [
      {
        id: "411ff751-daaa-4059-8507-78a61a12e710",
        description: "Add, edit, or remove taxi companies",
      },
      {
        id: "175f509f-3d97-4f81-9b30-fb726ea1b9ed",
        description: "Manage company subscription plans",
      },
      {
        id: "ad0f3df5-353a-4ea6-890e-6db504236b10",
        description:
          "View & modify company-specific pricing and fare structures",
      },
      {
        id: "05943395-8021-4021-815d-aeb996ec5f11",
        description: "Access & edit company billing information",
      },
    ],
  },
  {
    title: "System & Policy Settings",
    items: [
      {
        id: "edeab525-e49d-482d-bd5a-0b2589c08a0f",
        description: "Define platform-wide fare policies",
      },
      {
        id: "a4161a2d-b100-41bb-99a1-c886f3ceb082",
        description: "Set geofencing rules & restrictions",
      },
      {
        id: "890688be-59ff-4cad-9964-56f7b3fc5ec8",
        description: "Control global discount and promo policies",
      },
      {
        id: "dca62c28-d697-422c-8bdc-9769cdd1f14b",
        description: "Configure ride cancellation policies",
      },
    ],
  },
  {
    title: "Reporting & Analytics",
    items: [
      {
        id: "f56f448d-cb9f-441d-819a-bc8b8d67110a",
        description:
          "View and export reports on revenue, ride activity, and system performance",
      },
      {
        id: "2e23460d-193d-40d5-9bae-3c2d52bae2da",
        description: "Monitor driver performance & customer ratings",
      },
      {
        id: "1ed43a60-f5cb-4466-b254-ce7a20128df9",
        description: "Analyze dispatcher efficiency",
      },
    ],
  },
];

/* ============================ Page ============================ */

export default function MyClients() {
  const [activeTab, setActiveTab] = useState<"Super Admins" | "My Clients">(
    "Super Admins",
  );
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState<SuperAdminType | null>(null);
  const [editUser, setEditUser] = useState<SuperAdminType | null>(null);
  const modalRef = useRef<HTMLDivElement | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [editIsActive, setEditIsActive] = useState<boolean>(true);
  const [addModalOpen, setAddModalOpen] = useState(false);
   const [editFormData, setEditFormData] = useState({
      firstName: "",
      isActive: false
    });

  const { push } = useRouter();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "test",
    email: "",
    password: "",
    role: "super_admin",
  });

  /* --------------------------- Queries --------------------------- */

  const getSuperAdmins = () => {
    return backendApiClient.get("superadmin").json<SuperAdminsResponse>();
  };

  const getClients = () => {
    return backendApiClient.get("clients").json<ClientsResponse>();
  };

  const { data: superAdminsData, isLoading: isLoadingSuperAdmins } =
    useQuery<SuperAdminsResponse>({
      queryKey: ["superAdmins"],
      queryFn: getSuperAdmins,
      enabled: true,
    });

  const { data: clientsData, isLoading: isLoadingClients } =
    useQuery<ClientsResponse>({
      queryKey: ["clients"],
      queryFn: getClients,
      enabled: true,
    });

  /* ------------------- Permission selection ------------------- */

  function handleCheckboxPress(id: string) {
    setSelectedPermissions((prev) =>
      prev.includes(id) ? prev.filter((pid) => pid !== id) : [...prev, id],
    );
  }

  /* ---------------------------- Add ---------------------------- */

  async function addSuperAdmin(data: any) {
    return await backendApiClient.post("superadmin", { json: data }).json();
  }

  const { mutate: addSuperAdminMutation } = useMutation({
    mutationFn: addSuperAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
      setAddModalOpen(false);
      setSelectedPermissions([]);
      setFormData({
        firstName: "",
        lastName: "test",
        email: "",
        password: "",
        role: "super_admin",
      });
      toast.success("Super Admin Added Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to add Super Admin");
    },
  });

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    if (!formData.firstName || !formData.email) {
      toast.error("Please fill in all fields");
      return;
    }
    if (selectedPermissions.length === 0) {
      toast.error("Please select at least one permission");
      return;
    }
    const dataToSend = {
      ...formData,
      permissionIds: selectedPermissions,
    };

    addSuperAdminMutation(dataToSend);
  }

  /* --------------------------- Delete -------------------------- */

  async function deleteSuperAdmin(id: string) {
    return await backendApiClient.delete(`superadmin/${id}`).json();
  }

  const { mutate: deleteSuperAdminMutation } = useMutation({
    mutationFn: deleteSuperAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
      toast.success("Super Admin Deleted Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to delete Super Admin");
    },
  });

  async function deleteClient(id: string) {
    return await backendApiClient.delete(`clients/${id}`).json();
  }

  const { mutate: deleteClientMutation } = useMutation({
    mutationFn: deleteClient,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      toast.success("clients Deleted Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to delete clients");
    },
  });

  /* --------------------------- Update -------------------------- */

  async function updateSuperAdmin({
    id,
    payload,
  }: {
    id: string;
    payload: { permissionIds?: string[]; isActive?: boolean , firstName?: string,
    };
  }) {
    return await backendApiClient
      .patch(`superadmin/${id}`, { json: payload })
      .json();
  }

  const { mutate: updateSuperAdminMutation, isPending: isUpdating } =
    useMutation({
      mutationFn: updateSuperAdmin,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
        toast.success("Super Admin Updated Successfully", {
          autoClose: 5000,
          position: "top-center",
        });
        setEditUser(null);
        setSelectedPermissions([]);
      },
      onError: (err) => {
        console.error(err);
        toast.error("Failed to update Super Admin");
      },
    });

  const handleSaveEdit = () => {
    if (!editUser) return;
    updateSuperAdminMutation({
      id: editUser.id,
      payload: {
        permissionIds: selectedPermissions,
        firstName: editFormData.firstName,
        isActive: editFormData.isActive,
      },
    });
  };

  const updateClientStatusAPI = ({
    id,
    status,
  }: {
    id: string;
    status: string;
  }) => {
    return backendApiClient
      .patch(`clients/${id}/status`, { json: { status } })
      .json();
  };
  const handleStatusChange = (id: string, status: string) => {
    updateClientStatus({ id, status });
  };

  const { mutate: updateClientStatus } = useMutation({
    mutationFn: updateClientStatusAPI,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      toast.success(" Client Status Updated Successfully", {
        autoClose: 3000,
        position: "top-center",
      });
      setEditUser(null);
      setSelectedPermissions([]);
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to update Client Status");
    },
  });

  /* When opening edit, prime permission IDs + isActive */
  useEffect(() => {
    if (editUser) {
      setSelectedPermissions(editUser.permissions?.map((p) => p.id) ?? []);
      setEditFormData({
        firstName: editUser.firstName,
        isActive: editUser.isActive,
      });
    }
  }, [editUser]);

  return (
    <div className="p-6">
      {/* Tabs */}
      <div className="p-6">
        {/* Tabs */}
        <div className="flex items-center justify-between">
          <div className="flex gap-6">
            {["Super Admins", "My Clients"].map((tab) => (
              <button
                key={tab}
                onClick={() =>
                  setActiveTab(tab as "Super Admins" | "My Clients")
                }
                className={`relative pb-3 text-[13px] ${
                  activeTab === tab
                    ? "font-semibold text-[#3324E3] after:absolute after:bottom-0 after:left-0 after:h-[3px] after:w-full after:rounded-full after:bg-[#3324E3] after:content-['']"
                    : "text-[#76787A]"
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
          <button
            onClick={() => {
              if (activeTab === "Super Admins") {
                setAddModalOpen(true);
              } else {
                push("/super-admin/add-new-client/");
              }
            }}
            className="flex items-center gap-2 rounded-full bg-[#3324E3] px-4 py-2 text-xs font-medium text-white sm:text-sm"
          >
            <HiPlus />
            New {activeTab === "Super Admins" ? "Super Admin" : "Client"}
          </button>
        </div>

        {/* Table */}
        <div className="mt-6 overflow-visible rounded-xl border border-gray-200 bg-white">
          <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
            <form
              className="max-w-md flex-1"
              onSubmit={(e) => e.preventDefault()}
            >
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                  <IoIosSearch size={20} />
                </div>
                <input
                  id="default-search"
                  type="search"
                  placeholder="Search here"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="block w-3/4 rounded-full border border-gray-300 bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </form>

            <div className="flex items-center gap-2">
              <button
                type="button"
                className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#13BB76] hover:bg-gray-100 focus:outline-none"
              >
                <span className="h-2 w-2 rounded-full bg-[#13BB76]" />
                Active
              </button>
              <button
                type="button"
                className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 focus:outline-none"
              >
                <span className="h-2 w-2 rounded-full bg-[#76787A]" />
                Inactive
              </button>
              <button
                type="button"
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100"
              >
                <FiFilter size={18} />
                Filters
              </button>
              <button
                aria-label="clock"
                type="button"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100"
              >
                <RxCounterClockwiseClock size={22} />
              </button>
              <button
                aria-label="download"
                type="button"
                className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100"
              >
                <FiDownload size={16} />
              </button>
            </div>
          </div>

          <div className="custom-scrollbar max-w-full overflow-x-auto">
            <div className="min-w-[-webkit-fill-available]">
              {/* Conditionally render the correct table component */}
              {activeTab === "Super Admins" ? (
                isLoadingSuperAdmins ? (
                  <p className="p-4 text-center">Loading...</p>
                ) : (
                  <SuperAdminsTable
                    data={superAdminsData?.data || []}
                    onView={setSelectedUser}
                    onEdit={(user) => {
                      setEditUser(user);
                      setSelectedPermissions(
                        user.permissions.map((item) => item.id),
                      );
                    }}
                    onDelete={deleteSuperAdminMutation}
                  />
                )
              ) : isLoadingClients ? (
                <p className="p-4 text-center">Loading...</p>
              ) : (
                <ClientsTable
                  data={clientsData?.data || []}
                  onDelete={deleteClientMutation}
                  onStatusChange={handleStatusChange}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* add new super admin modal */}
      {addModalOpen && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div className="absolute inset-0 bg-black/30"></div>
          <div
            ref={modalRef}
            className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out"
          >
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                New Super Admins
              </h2>
              <button
                onClick={() => setAddModalOpen(false)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <form className="flex flex-col gap-6" onSubmit={handleSubmit}>
              <div className="px-6 py-3">
                <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
                  Basic Information
                </h3>
                <input
                  type="text"
                  required
                  onChange={(e) =>
                    setFormData({ ...formData, firstName: e.target.value })
                  }
                  placeholder="Full Name"
                  className="mb-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="email"
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  required
                  placeholder="Email ID"
                  className="w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="password"
                  autoComplete="off"
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  required
                  minLength={6}
                  placeholder="password"
                  className="mt-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="px-6 py-3">
                <h3 className="mb-4 text-[14px] font-medium text-[#050013]">
                  Access Control and Permissions
                </h3>
                {accessSections.map((section, idx) => (
                  <AccordionSection
                    key={idx}
                    title={section.title}
                    items={section.items}
                    onPress={handleCheckboxPress}
                    selectedPermissions={selectedPermissions}
                  />
                ))}
              </div>

              <div className="sticky bottom-0 border-t bg-white px-6 py-3">
                <button
                  type="submit"
                  className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0]"
                >
                  Add
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* View detail modal */}

      {selectedUser && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setSelectedUser(null)}
          />
          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                View User Details
              </h2>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              <div>
                <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  General Details
                </h3>
                <div className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      User Name:
                    </span>{" "}
                    {selectedUser.firstName} {selectedUser.lastName}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Role Name:
                    </span>{" "}
                    {selectedUser.role}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Created Date:
                    </span>{" "}
                    {formatDate(selectedUser.createdAt)}
                  </div>
                  <div className="mb-4 grid text-[12px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Status:
                    </span>
                    <span
                      className={`ml-1 inline-flex items-center gap-1 ${
                        selectedUser.isActive
                          ? "text-[#13BB76]"
                          : "text-[#8C8B9F]"
                      }`}
                    >
                      <span className="inline-block h-2 w-2 rounded-full bg-current" />
                      {selectedUser.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#13BB76]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Active:
                    </span>
                    {selectedUser.lastLoginAt
                      ? formatDate(selectedUser.lastLoginAt)
                      : "—"}
                  </div>
                </div>
              </div>
              <div className="mb-12">
                <h3 className="rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Access Control And Permissions
                </h3>
                <div className="px-6 py-3">
                  <ul className="ml-5 list-disc text-[#050013]">
                    {selectedUser.permissions?.map((p) => (
                      <li key={p.id} className="my-1">
                        {p.description}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div>
                <h3 className="mb-1 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Edit Logs
                </h3>
                <div className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Modified:
                    </span>{" "}
                    {formatDate(selectedUser.updatedAt)}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Modified By:
                    </span>{" "}
                    —
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* edit modal */}
      {editUser && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setEditUser(null)}
          />
          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                Edit Super Admin Details
              </h2>
              <button
                onClick={() => {
                  setEditUser(null);
                  setSelectedPermissions([]);
                }}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            {/* General Details */}
            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              <div>
                <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  General Details
                </h3>
                <div className="grid grid-cols-2 gap-4 px-4 py-4">
                  {/* User Name (read-only combined for now) */}
                  <div className="relative w-full">
                    <input
                      type="text"
                      id="username"
                      value={editFormData.firstName}
                      onChange={(e) =>
                        setEditFormData({ ...editFormData, firstName: e.target.value })
                      }
                      placeholder="User Name"
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] placeholder-transparent focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="username"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      User Name
                    </label>
                  </div>

                  {/* Role Name (read-only) */}
                  <div className="relative w-full">
                    <input
                      id="role"
                      value={editUser.role}
                      readOnly
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="role"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Role Name
                    </label>
                  </div>

                  {/* Created Date */}
                  <div className="relative w-full">
                    <input
                      type="text"
                      id="createdDate"
                      value={formatDate(editUser.createdAt)}
                      readOnly
                      placeholder="Created Date"
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] placeholder-transparent focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="createdDate"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Created Date
                    </label>
                  </div>

                  {/* Status (Active/Inactive) */}
                  <div className="relative w-full">
                    <select
                      id="status"
                      value={editFormData.isActive ? "Active" : "Inactive"}
                      onChange={(e) =>
                        setEditFormData({ ...editFormData, isActive: e.target.value === "Active" })
                      }
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    >
                      <option>Active</option>
                      <option>Inactive</option>
                    </select>
                    <label
                      htmlFor="status"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Status
                    </label>
                  </div>
                </div>
              </div>

              {/* Access Control Section */}
              <div className="mb-16">
                <h3 className="mb-6 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Access Control And Permissions
                </h3>
                <div className="px-2">
                  {accessSections.map((section, idx) => {
                    return (
                      <AccordionSection
                        key={idx}
                        title={section.title}
                        items={section.items}
                        onPress={handleCheckboxPress}
                        selectedPermissions={selectedPermissions}
                      />
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 border-t bg-white px-6 py-3">
              <button
                type="button"
                disabled={isUpdating}
                className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0] disabled:opacity-60"
                onClick={handleSaveEdit}
              >
                {isUpdating ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
