"use client";
import React, { useEffect, useMemo, useState } from 'react';
import { BsThreeDotsVertical } from "react-icons/bs";
import { BsDownload } from "react-icons/bs";
import { TbEdit, TbNotes, TbRotateClockwise2, TbFileLike } from "react-icons/tb";
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import { Activityicon, CheckboxTickIcon, DocumentIcon, DocumentthumnIcon, DollarIcon, DollartimeIcon, EarningIcon, EditIcon, StarIcon, StarsIcon } from '@/icons';
import { CiDollar } from "react-icons/ci";
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import Driveractivitytable from './Driveractivitytable';
import Supportlog from './Supportlog';
import DocumentTab from './Documenttab';
import EarningTable from './EarningTable';
import DeleteModal from '@/components/ui/modal/deletemodal';
import { useMutation, useQuery, } from "@tanstack/react-query";
import { deleteDriver, disableDriverQuery, downloadReport, editDriverDetails, getSpecificDriverDetails, getTableDriverDetails, } from "../state/queries";
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { queryClient } from '@/hooks/useGlobalContext';
import DisableDriver from './Disabledrive';
import { toast, ToastContainer } from 'react-toastify';
import Input from "../../../../../components/form/input/InputField";
import Select from "../../../../../components/form/Select";
import ChevronDownIcon from "../../../../../icons/chevron-down.svg"
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const menuItems = [
    [
        "Disable Driver",
        "Send Notification",
        "Send Message",
        "Export Driver Report",
        "Delete profile"
    ],
    [
        "Bulk Resolve Issues",
        "Approve All Pending Trips",
        "Export Activity Data",
        "Generate Activity Report",
        "Report Suspicious Activity",
        "Download Receipts of all Trips",
    ],
    [
        "Approve Selected",
        "Reject Selected",
        "Send Reminder",
        "Download",
        "Export Data",
        "Update Expiry Date",
        "Add Comments",
        "Send Message",
        "Delete Documents"
    ],
    [
        "View Details",
        "Resolve Issue",
        "Re-Open Issue",
        "Re-Assign to New Admin",
        "Escalate Issue",
        "Add Comment",
        "Download Log",
        "Delete Entry"
    ]
]


export default function Driverdetails() {
    const router = useRouter();
    const params = useParams();

    const driverId = params?.driverdetails as string;

    const [activeTab, setActiveTab] = useState(0);
    const [deleteProfile, setDeleteProfile] = useState(false);
    const [disableDriver, setDisableDriver] = useState(false);
    const [isEditProfile, setIsEditProfile] = useState(false);
    const [driverDetails, setDriverDetails] = useState<any>({})
    const searchParams = useSearchParams();
    const [tippyVisible, setTippyVisible] = useState<boolean>(false);
    const [formData, setFormData] = useState({
        fullName: "",
        email: "",
        gender: "",
        dob: "",
        nationalIdSsn: "",
        vehicleAvailability: "",
        model: "",
        plateNumber: "",
        vehicleType: "",
        address: "",
        countryCode: "+1",
        contactNo: "9876543210",
        shift: "morning",
        region: "North",
        rating: "4.5",
        city: "",
        state: "",
        zipCode: "",
        color: "",
        lastActive: "",
        driverStatus: "",
        insuranceExpiryDate: "2030-12-31",
        insuranceRenewalReminder: true,
        vehicleRegistration: "true",
        vehicleDetails: "vehicleDetails",
        registrationExpiryDate: "",
        registrationStatus: "",
        image: null as File | null,
    });

    const { data: driverData, } = useQuery({
        queryKey: ["driverDetails", driverId],
        queryFn: async () => {
            return getSpecificDriverDetails(driverId);
        },
        enabled: !!driverId,
    });

    const deleteDriverMutation = useMutation({
        mutationFn: (data: string) => {
            return deleteDriver(data);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["driverData"] });
            setDeleteProfile(false)
            toast.success('Driver Deleted Successfully', { autoClose: 5000, position: "top-center", })
        },
        onError: (err) => {
            console.error(err)
        },
    });
    const disableDriverMutation = useMutation({
        mutationFn: async (data: any) => {
            return disableDriverQuery(data);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["driverDetails"] });
            setDisableDriver(false)
            toast.success("Driver Disabled Successfully", { autoClose: 5000, position: "top-center", })

        },
        onError: (err) => {
            console.error(err)

        },
    });
    const handleDeleteDriver = () => {
        if (driverId) {
            deleteDriverMutation.mutate(driverId);
            router.push("/driver")
        }
    };
    const handleDisableDriver = () => {
        const data = { driverId, status: "Inactive" }

        if (driverId) {
            disableDriverMutation.mutate(data);
        }
    };


    const downLoadDriverMutation = useMutation({
        mutationFn: () => downloadReport(driverId),
        onSuccess: async (res) => {
            const url = window.URL.createObjectURL(new Blob([res], { type: 'text/csv' }));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'data.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        },
        onError: (err) => {
            console.error("Download error", err);
        },
    });

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };
    const handleClick = () => {
        toast.success("Notification sent!", { autoClose: 5000, position: "top-center", });
    };

    const editDriverDriverMutation = useMutation({
        mutationFn: async (data: FormData) => {
            return editDriverDetails(data, driverId);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["driverDetails"] });
            setIsEditProfile(false)
            toast.success("Profile Edited successfully", { autoClose: 5000, position: "top-center", })
        },
        onError: (err) => {
            console.error(err)

        },
    });

    const handleFormSubmit = () => {
        const payLoad = objectToFormData(formData);
        console.log(payLoad)
        if (payLoad) {
            editDriverDriverMutation.mutate(payLoad)
        }
    };

    const objectToFormData = (obj) => {

        const formData = new FormData();
        delete obj.id;
        Object.entries(obj).forEach(([key, value]) => {

            if (value !== null && value !== undefined) {
                if (value instanceof File || value instanceof Blob) {
                    formData.append(key, value, value?.name);
                } else {
                    formData.append(key, value);
                }
            }
        });
        return formData;
    };
    const modalYearOptions = Array.from({ length: 2026 - 2000 }, (_, i) => ({
        value: `${2000 + i}`,
        label: `${2000 + i}`
    }));

    const handleDateChange = (date: Date | null, fieldName: string) => {
        setFormData(prev => ({
            ...prev,
            [fieldName]: date ? date.toISOString().split('T')[0] : "" // Format as YYYY-MM-DD
        }));
    };

    const vehicleOptions = [
        { value: "sedan", label: "Sedan" },
        { value: "van", label: "Van" },
        { value: "mini-van", label: "Mini Van" },
        { value: "suv", label: "SUV" },
    ]

    const getTippyOptions = () => {
        switch (activeTab) {
            case 0:
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">

                            <DisableDriver isOpen={disableDriver} setPropTippyVisible={setTippyVisible} setIsOpen={setDisableDriver} handleDisableDriver={handleDisableDriver} />
                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start"
                                onClick={() => { setTippyVisible(false); handleClick }}
                            >
                                Send Notification
                            </button>
                            <button onClick={() => { setTippyVisible(false) }}
                                className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                <a href={`https://wa.me/${driverDetails?.contactNo}`} target="_blank" rel="noopener noreferrer">
                                    Send Message
                                </a>
                            </button>
                            <button
                                onClick={() => { setTippyVisible(false); downLoadDriverMutation.mutate() }}
                                className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Export Driver Report

                            </button>

                            <DeleteModal isOpen={deleteProfile} setPropTippyVisible={setTippyVisible} setIsOpen={setDeleteProfile} handleDeleteDriver={handleDeleteDriver}
                                id={driverDetails?.id}
                            />

                        </div>
                    </div >
                );
            case 1: // Activity
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Bulk Resolve Issues
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Approve All Pending Trips
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Export Activity Data
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Report Suspicious Activity
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Generate Activity Report
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Download Receipts of all Trips
                            </button>
                        </div>
                    </div>
                );
            case 2: // Earning
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Export Earnings Report
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Adjust Earnings
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Payment History
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Set Payment Schedule
                            </button>
                        </div>
                    </div>
                );
            case 3: // Documents
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Approve Selected
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Reject Selected
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Send Reminder
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Download
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Export Data
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Update Expiry Date
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Add Comments
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Send Message
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Delete Documents
                            </button>
                        </div>
                    </div>
                );
            case 4: // Support Log
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                View Details
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Resolve Issue
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Re Opne Issue
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Re-Assign to New Admin
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Escalate Issue
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Add Comment
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Download Log
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Delete Entry
                            </button>
                        </div>
                    </div>
                );
            default:
                return (
                    <div className="bg-white text-gray-900">
                        <div className="flex flex-col space-y-1 p-1">
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Disable Driver
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Send Notification
                            </button>
                            <button
                                className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                <a href={`https://wa.me/${driverDetails?.contactNo}`} target="_blank" rel="noopener noreferrer">
                                    Send Message
                                </a>
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                Export Driver Report
                            </button>
                            <button onClick={() => { setTippyVisible(false) }} className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start"

                            >
                                Delete Profile
                            </button>
                        </div>
                    </div>
                );
        }
    };

    const getDriverStatusClass = (status) => {
        if (status === "Active") return "text-[#13BB76]";
        if (status === "Inactive") return "text-[#8F8CD6]";
        if (status === "Suspended") return "text-[#FF4032]";
        if (status === "Enroute") return "text-[#1E90FF]";
        return "text-[#FF8C00]";
    };

    useEffect(() => {
        if (driverData) {
            setFormData(driverData[0])
            setDriverDetails(driverData[0])
        }
    }, [driverData, driverId])
    useEffect(() => {
        setIsEditProfile(searchParams.get("edit") === "true");
    }, [searchParams, params, driverId]);

    const [imageUrl, setImageUrl] = useState("");

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        setFormData(prev => ({
            ...prev,
            image: file
        }));
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setImageUrl(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };
    console.log(activeTab, "activeTab");


    return (
        <>
            <ToastContainer />
            <div className=" space-y-6 border border-gray-200 rounded-xl ">
                {/* Card Header */}
                <div className="flex items-top justify-between rounded-xl rounded-b-none bg-[#ffffff] p-6 mb-0">
                    <div className="flex items-center space-x-4">

                        <div className="relative w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32">
                            {isEditProfile ? (
                                <label
                                    htmlFor="profileImageUpload"
                                    className="group w-full h-full rounded-full cursor-pointer flex items-center justify-center bg-gray-100 overflow-visible  relative shadow-md transition-all duration-200 hover:shadow-lg"
                                >
                                    <input
                                        id="profileImageUpload"
                                        type="file"
                                        accept="image/*"
                                        className="hidden"
                                        onChange={handleImageChange}
                                    />
                                    <div className="absolute bottom-2 right-2 bg-white p-1.5 rounded-full shadow-md group-hover:scale-110 z-[1] transition-transform duration-200">
                                        <svg
                                            className="w-4 h-4 text-gray-700"
                                            fill="none"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            viewBox="0 0 24 24"
                                        >
                                            <path d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3z" />
                                            <path d="M16 16h2a2 2 0 002-2v-1" />
                                        </svg>
                                    </div>
                                    <img
                                        src={
                                            imageUrl ||
                                            driverDetails?.image ||
                                            "https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg"
                                        }
                                        alt="Profile"
                                        className="w-full h-full rounded-full object-cover transition-transform duration-200 group-hover:scale-105"
                                    />

                                    {/* <div className="absolute bottom-2 right-2 bg-white p-1.5 rounded-full shadow-md group-hover:scale-110 transition-transform duration-200">
                                        <svg
                                            className="w-4 h-4 text-gray-700"
                                            fill="none"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            viewBox="0 0 24 24"
                                        >
                                            <path d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3z" />
                                            <path d="M16 16h2a2 2 0 002-2v-1" />
                                        </svg>
                                    </div> */}
                                </label>
                            ) : (
                                <img
                                    src={
                                        driverDetails?.image ||
                                        "https://www.twtf.org.uk/wp-content/uploads/2024/01/dummy-image.jpg"
                                    }
                                    alt="Profile"
                                    className="w-full h-full rounded-full object-cover shadow-md"
                                />
                            )}
                        </div>


                        <div>
                            {isEditProfile ?

                                <Input type="text"
                                    placeholder="Full Name"
                                    className="mb-3"
                                    onChange={handleChange}
                                    defaultValue={driverDetails?.fullName}
                                    name="fullName"
                                />

                                :
                                <h2 className="text-[24px] font-regular text-[#050013]">{driverDetails?.fullName}</h2>
                            }

                            <div className='flex items-center space-x-2'><span className='w-2 h-2 bg-active-status rounded-full'></span><p className={`text-[11px] ${getDriverStatusClass(driverDetails?.driverStatus)}`}>
                                {driverDetails?.driverStatus}
                            </p></div>
                            {/* <span className={`text-sm ${driverDetails?.driverStatus ? "text-[#13BB76]" : "text-[#e80e0e]"} `}> {driverDetails?.driverStatus}</span> */}
                        </div>
                    </div>

                    {activeTab === 2 ?
                        <>
                            <div className='pl-6 border-l border-grey-200'>
                                <div className='flex flex-col gap-1 '>
                                    <div className='w-[40px] h-[40px] bg-[#D7FFEF] flex justify-center items-center rounded-full'> <DollarIcon /></div>
                                    <h2 className="text-[16px] font-regular text-gray-800 font-semibold">€8,031.00</h2>
                                    <span className="text-xs text-[#76787A]"> Total Earnings</span>
                                </div>
                            </div>
                            <div className='flex flex-col gap-1'>
                                <div className='w-[40px] h-[40px] bg-[#FFF3E7] flex justify-center items-center rounded-full'> <EarningIcon /></div>
                                <h2 className="text-[16px] font-regular text-gray-800 font-semibold">€8,031.00</h2>
                                <span className="text-xs text-[#76787A]"> Company Earnings</span>
                            </div>
                            <div className='flex flex-col gap-1'>
                                <div className='w-[40px] h-[40px] bg-[#FFEEEE] flex justify-center items-center rounded-full'> <DollartimeIcon /></div>
                                <h2 className="text-[16px] font-regular text-gray-800 font-semibold">€8,031.00</h2>
                                <span className="text-xs text-[#76787A]"> Pending Payment</span>
                            </div>
                            <div className='flex flex-col gap-1'>
                                <div className='w-[40px] h-[40px] bg-[#D7FFFC] flex justify-center items-center rounded-full'> <StarsIcon /></div>
                                <h2 className="text-[16px] font-regular text-gray-800 font-semibold">€8,031.00</h2>
                                <span className="text-xs text-[#76787A]"> Bonuses Earned</span>
                            </div>
                            <div className='flex flex-col gap-1'>
                                <div className='w-[40px] h-[40px] bg-[#EFEEFF] flex justify-center items-center rounded-full'> <CheckboxTickIcon /></div>
                                <h2 className="text-[16px] font-regular text-gray-800 font-semibold">€8,031.00</h2>
                                <span className="text-xs text-[#76787A]"> Total Trips Completed</span>
                            </div>
                        </>
                        : null}

                    <div className="flex gap-3 items-start hover-effect">
                        {isEditProfile && activeTab === 0 ? (
                            <button
                                className="flex items-center gap-2 text-white bg-cstm-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-4 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                onClick={handleFormSubmit}
                            >
                                Save
                            </button>
                        ) : (
                            <button
                                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-dark-grey"
                                onClick={() => {
                                    activeTab == 0 && setIsEditProfile(true);
                                }}
                            >
                                <TbEdit size={20} />
                            </button>
                        )}


                        {activeTab != 2 && (
                            <>
                                <button
                                    className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-dark-grey"
                                    onClick={() => downLoadDriverMutation.mutate()}
                                >
                                    <BsDownload size={20} />
                                </button>

                                <Tippy
                                    className="!bg-white !text-gray-900 border border-gray-200"
                                    trigger="click"
                                    interactive={true}
                                    placement="bottom-end"
                                    arrow={false}
                                    content={getTippyOptions()}
                                    visible={tippyVisible}
                                    onClickOutside={() => setTippyVisible(false)}
                                >
                                    <button
                                        type="button"
                                        className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 text-dark-grey"
                                        onClick={() => setTippyVisible(true)}
                                    >
                                        <BsThreeDotsVertical size={20} />
                                    </button>
                                </Tippy>
                            </>
                        )}
                    </div>


                </div>


                <TabGroup selectedIndex={activeTab} onChange={setActiveTab}>
                    <TabList as="aside" className="flex gap-[18px] px-[43px] border-t border-gray-200 pt-5 bg-white">
                        <Tab className="flex gap-2 text-sm pb-[15px] items-center mr-[25px] text-[#76787A] focus:outline-none focus:ring-0 data-[selected]:font-semibold data-[selected]:text-[#3324E3] data-[selected]:border-b-[#3324E3] data-[selected]:border-b-[3px] hover:text-[#3324E3]" value="generalInfo" ><TbNotes className='w-[24px] h-[24px]' />General Info </Tab>
                        <Tab className="flex gap-2 text-sm pb-[15px] items-center mr-[25px] text-[#76787A] focus:outline-none focus:ring-0 data-[selected]:font-semibold data-[selected]:text-[#3324E3] data-[selected]:border-b-[#3324E3] data-[selected]:border-b-[3px] hover:text-[#3324E3]" value="activity" > <TbRotateClockwise2 className='w-[24px] h-[24px]' />Activity</Tab>
                        <Tab className="flex gap-2 text-sm pb-[15px] items-center mr-[25px] text-[#76787A] focus:outline-none focus:ring-0  data-[selected]:font-semibold data-[selected]:text-[#3324E3] data-[selected]:border-b-[#3324E3] data-[selected]:border-b-[3px] hover:text-[#3324E3]" value="earning"><CiDollar className='w-[24px] h-[24px]' />Earnings</Tab>
                        <Tab className="flex gap-2 text-sm pb-[15px] items-center mr-[25px] text-[#76787A] focus:outline-none focus:ring-0 data-[selected]:font-semibold data-[selected]:text-[#3324E3] data-[selected]:border-b-[#3324E3] data-[selected]:border-b-[3px] hover:text-[#3324E3]" value="documents"><TbFileLike className='w-[24px] h-[24px]' />Documents</Tab>
                        <Tab className="flex gap-2 text-sm pb-[15px] items-center text-[#76787A] focus:outline-none focus:ring-0 data-[selected]:font-semibold data-[selected]:text-[#3324E3] data-[selected]:border-b-[#3324E3] data-[selected]:border-b-[3px] hover:text-[#3324E3]" value="supportLog"><TbFileLike className='w-[24px] h-[24px]' />Support Log</Tab>
                    </TabList>

                    <TabPanels as="section" className=" bg-[#F6F8FB] rounded-b-[12px]"  >
                        <TabPanel className="px-[43px] py-[23px] rounded-bl-[12px] ">
                            <div className=' bg-[#F6F8FB]'>
                                <h3 className="text-md text-gray-500 mb-[20px] bg-white px-[27px] py-[9px] rounded-2xl">Driver Details</h3>
                                <div className="px-[27px] rounded-lg mb-[40px]">
                                    <div className="grid grid-cols-5 text-sm text-gray-700 gap-[20px]">

                                        {[
                                            { label: "Email Id", name: "email", value: driverDetails?.email },
                                            { label: "Mobile Number", name: "contactNo", value: driverDetails?.contactNo },
                                            { label: "Current Address", name: "address", value: driverDetails?.address },
                                            { label: "Permanent Address", name: "permanentAddress", value: driverDetails?.permanentAddress },
                                            { label: "City", name: "city", value: driverDetails?.city },
                                            { label: "State", name: "state", value: driverDetails?.state },
                                            { label: "Zip Code", name: "zipCode", value: driverDetails?.zipCode },
                                            {
                                                label: "Date Of Birth",
                                                name: "dob",
                                                value: driverDetails?.dob
                                                    ? new Date(driverDetails.dob).toISOString().slice(0, 10) // yyyy-mm-dd
                                                    : "--",
                                            },
                                            { label: "National ID/SSN", name: "nationalIdSsn", value: driverDetails?.nationalIdSsn },
                                            { label: "Emergency Contact", name: "emergencyContact", value: driverDetails?.emergencyContact },
                                            { label: "Emerg. Contact Person", name: "emergencyContactPerson", value: driverDetails?.emergencyContactPerson },
                                            { label: "Emg. Contact Relation", name: "emergencyContactRelation", value: driverDetails?.emergencyContactRelation },
                                        ].map((item, index) => (
                                            <div key={index}>
                                                <span className='text-gray-500 text-[13px] mb-2 block whitespace-nowrap'>{item.label}</span>

                                                {isEditProfile ?

                                                    <Input type="text"
                                                        placeholder={item?.label}
                                                        className="mb-3"
                                                        onChange={handleChange}
                                                        defaultValue={item?.value}
                                                        name={item?.name}
                                                    />
                                                    :
                                                    <>
                                                        <span className="text-[14px] font-medium text-[#050013]">{item.value}</span>
                                                    </>
                                                }
                                            </div>
                                        ))}
                                    </div>
                                </div>


                                <h3 className="text-md text-gray-500   bg-white px-[27px] py-[9px] rounded-2xl mb-[20px]">Documents and Verification details</h3>
                                <div className="px-[27px] rounded-lg mb-[40px]">
                                    <div className="grid grid-cols-5 gap-[44px] text-sm text-gray-700">
                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Vehicle Model</span>
                                            {isEditProfile ? (

                                                <div>
                                                    <div className="relative">
                                                        <Select
                                                            options={modalYearOptions}
                                                            placeholder="Model"
                                                            defaultValue={driverDetails?.model}
                                                            onChange={(e) =>
                                                                setFormData({ ...formData, "model": e })
                                                            }
                                                            className="dark:bg-dark-900 w-100"

                                                        />
                                                        <span
                                                            className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                                            <ChevronDownIcon />
                                                        </span>
                                                    </div>

                                                </div>

                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.model || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Plate Number</span>
                                            {isEditProfile ? (
                                                <Input
                                                    type="text"
                                                    placeholder="Plate Number"
                                                    className="mb-3"
                                                    onChange={handleChange}
                                                    defaultValue={driverDetails?.plateNumber || ""}
                                                    name="plateNumber"
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.plateNumber || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Vehicle Type</span>
                                            {isEditProfile ? (
                                                <div>
                                                    <div className="relative">
                                                        <Select
                                                            options={vehicleOptions}
                                                            placeholder="Vehicle Type"
                                                            onChange={(e) =>
                                                                setFormData({ ...formData, "vehicleType": e })
                                                            }
                                                            className="dark:bg-dark-900 w-100"
                                                            defaultValue={driverDetails?.vehicleType}
                                                        />
                                                        <span
                                                            className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                                            <ChevronDownIcon />
                                                        </span>
                                                    </div>
                                                </div>
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.vehicleType || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Vehicle Color</span>

                                            {isEditProfile ? (
                                                <Input
                                                    type="text"
                                                    placeholder="Vehicle Color"
                                                    className="mb-3"
                                                    onChange={handleChange}
                                                    defaultValue={driverDetails?.color || ""}
                                                    name="color"
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.color || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Registration Status</span>
                                            {isEditProfile ? (
                                                <Input
                                                    type="text"
                                                    placeholder="Registration Status"
                                                    className="mb-3"
                                                    onChange={handleChange}
                                                    defaultValue={driverDetails?.registrationStatus || ""}
                                                    name="registrationStatus"
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.registrationStatus || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Registration Expiry Date</span>
                                            {isEditProfile ? (
                                                <DatePicker
                                                    selected={formData.registrationExpiryDate ? new Date(formData.registrationExpiryDate) : null}
                                                    onChange={(date: Date | null) => handleDateChange(date, "registrationExpiryDate")}
                                                    className="w-full p-[11px] border border-gray-300 rounded-lg text-gray-800"
                                                    showYearDropdown
                                                    showMonthDropdown
                                                    scrollableYearDropdown
                                                    dateFormat="dd/MM/yyyy"
                                                    placeholderText="Registration Expiry Date"
                                                    minDate={new Date()}
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.registrationExpiryDate || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Insurance Expiry Date</span>

                                            {isEditProfile ? (
                                                <DatePicker
                                                    selected={formData.insuranceExpiryDate ? new Date(formData.insuranceExpiryDate) : null}
                                                    onChange={(date: Date | null) => handleDateChange(date, "insuranceExpiryDate")}
                                                    className="w-full p-[11px] border border-gray-300 rounded-lg text-gray-800"
                                                    showYearDropdown
                                                    showMonthDropdown
                                                    scrollableYearDropdown
                                                    dateFormat="dd/MM/yyyy"
                                                    placeholderText="Insurance Expiry Date"
                                                    minDate={new Date()}
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.insuranceExpiryDate
                                                        ? new Date(driverDetails.insuranceExpiryDate).toDateString()
                                                        : '--'}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Insurance Renewal Reminder</span>

                                            {isEditProfile ? (
                                                <div>
                                                    <div className="relative">
                                                        <Select
                                                            options={[{ label: "ON", value: "on" }, { label: "OFF", value: "off" }]}
                                                            placeholder="Insurance Renewal Reminder"
                                                            onChange={(e) =>
                                                                setFormData({ ...formData, "insuranceRenewalReminder": e === "on" ? true : false })
                                                            }
                                                            className="dark:bg-dark-900 w-100"
                                                            defaultValue={driverDetails?.insuranceRenewalReminder ? "on" : "off"}

                                                        />
                                                        <span
                                                            className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                                            <ChevronDownIcon />
                                                        </span>
                                                    </div>
                                                </div>

                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.insuranceRenewalReminder ? "ON" : "OFF"}</span>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>\
                                
                                <h3 className="text-md text-gray-500 mb-[20px] bg-white px-[27px] py-[9px] rounded-2xl">Professional Details</h3>
                                <div className="px-[27px] rounded-lg">
                                    <div className="grid grid-cols-5 gap-[44px] text-sm text-gray-700">
                                        {/* Assigned Region */}
                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Assigned Region</span>
                                            {isEditProfile ? (
                                                <Input
                                                    type="text"
                                                    placeholder="Assigned Region"
                                                    className="mb-3"
                                                    onChange={handleChange}
                                                    defaultValue={driverDetails?.region || ""}
                                                    name="region"
                                                />
                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.region || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Assigned Shift</span>

                                            {isEditProfile ? (
                                                <div>
                                                    <div className="relative">
                                                        <Select
                                                            options={[{ label: "Evening", value: "evening" }, { label: "Morning", value: "morning" }]}
                                                            placeholder="Shift"
                                                            onChange={(e) =>
                                                                setFormData({ ...formData, "vehicleType": e })
                                                            }
                                                            className="dark:bg-dark-900 w-100"
                                                            defaultValue={driverDetails?.shift}

                                                        />
                                                        <span
                                                            className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                                            <ChevronDownIcon />
                                                        </span>
                                                    </div>
                                                </div>

                                            ) : (
                                                <>
                                                    <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.shift || "--"}</span>
                                                </>
                                            )}
                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Last Active</span>
                                            <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.lastActive || "--"}</span>


                                        </div>

                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Trips Today</span>
                                            <span className='text-[14px] font-medium text-[#050013]'>{driverDetails?.tripsToday || "--"}</span>

                                        </div>
                                        <div>
                                            <span className="text-gray-500 text-[13px] whitespace-nowrap mb-2 block">Average Rating</span>
                                            <p className='text-[14px] font-medium text-[#050013] flex'>{driverDetails?.rating} <StarIcon /></p>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </TabPanel >
                        <TabPanel>
                            <Driveractivitytable driverDetails={driverDetails} />
                        </TabPanel>
                        <TabPanel>
                            <EarningTable driverDetails={driverDetails} />
                        </TabPanel>
                        <TabPanel><DocumentTab driverDetails={driverDetails} /></TabPanel>
                        <TabPanel><Supportlog driverDetails={driverDetails} /></TabPanel>
                    </TabPanels >
                </TabGroup >

            </div >


        </>
    )
}
